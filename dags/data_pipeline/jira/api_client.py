import asyncio
import os
import random
import sys
import threading
import traceback
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from logging import Logger
import socket
from typing import Union, Dict, Any, Optional, List

import aiohttp
from dateutil import parser
from dependency_injector.wiring import inject, Provide

from dags.data_pipeline.containers import (
    LoggerContainer,  ApplicationContainer, ErrorType, CircuitBreakerContainer, SimplifiedCircuitBreaker
)
from dags.data_pipeline.debug.debug_utils import rate_limit_tracker


MAX_RETRIES = 5
INITIAL_RETRY_DELAY = 5000.0  # in milliseconds
MAX_RETRY_DELAY = 10000  # in milliseconds
JITTER_MULTIPLIER_RANGE = (0.5, 1.5)


@dataclass
class ConnectorMetrics:
    """Structured connector metrics for database storage"""
    # Session identification
    session_name: str
    session_id: str
    timestamp: datetime

    # Connection status
    session_closed: bool
    connector_closed: bool
    connector_type: str

    # Connection limits
    total_limit: int
    per_host_limit: int

    # Connection statistics
    total_acquired: int
    total_available: int
    active_hosts: int
    global_acquired_pool_size: int

    # Utilization metrics
    total_utilization_pct: float
    is_at_capacity: bool
    is_near_capacity: bool  # >80% utilization

    # Health indicators
    health_status: str  # 'healthy', 'warning', 'critical', 'idle'
    has_cleanup_task: bool
    cleanup_task_active: bool

    # Per-host details (for JSONB storage)
    host_details: List[Dict[str, Any]]

    # System context
    process_id: int
    thread_id: int
    hostname: str

    # Error information (if any)
    error_occurred: bool
    error_message: Optional[str] = None
    error_line: Optional[int] = None


class ConnectorLogger:
    """Handles both visual and database-friendly connector logging"""

    def __init__(self, logger: Optional[Logger] = None):
        self.logger = logger
        self.hostname = socket.gethostname()
        self.process_id = os.getpid()

    def log_connector_attrs(
            self,
            name: str,
            session: aiohttp.client.ClientSession,
            correlation_id: Optional[str] = None,
            task_id: Optional[str] = None,
            visual_output: bool = True,
            return_metrics: bool = True
    ) -> Optional[ConnectorMetrics]:
        """
        Log connector attributes with both visual and structured output.

        Args:
            name: Descriptive name for this session/context
            session: The aiohttp ClientSession to inspect
            correlation_id: Correlation ID for tracking related operations
            task_id: Task ID for async operations
            visual_output: Whether to produce visual console/file output
            return_metrics: Whether to return structured metrics for DB storage

        Returns:
            ConnectorMetrics object if return_metrics=True, else None
        """
        # Handle both actual logger and Provide objects
        try:
            if hasattr(self.logger, 'debug') and callable(self.logger.debug):
                log = self.logger.debug
            elif self.logger:
                # If it's a Provide object or other non-logger, use print as fallback
                log = print
            else:
                log = print
        except Exception:
            log = print

        # Initialize metrics
        metrics = None
        error_occurred = False
        error_message = None
        error_line = None

        try:
            # Get basic session info
            session_id = str(id(session))
            timestamp = datetime.now()

            conn = session._connector
            if not conn:
                if visual_output:
                    log(f"❌ No connector found for session: {name}")

                if return_metrics:
                    metrics = ConnectorMetrics(
                        session_name=name,
                        session_id=session_id,
                        timestamp=timestamp,
                        session_closed=session.closed,
                        connector_closed=True,
                        connector_type="None",
                        total_limit=0,
                        per_host_limit=0,
                        total_acquired=0,
                        total_available=0,
                        active_hosts=0,
                        global_acquired_pool_size=0,
                        total_utilization_pct=0.0,
                        is_at_capacity=False,
                        is_near_capacity=False,
                        health_status="no_connector",
                        has_cleanup_task=False,
                        cleanup_task_active=False,
                        host_details=[],
                        process_id=self.process_id,
                        thread_id=threading.get_ident() if hasattr(threading, 'get_ident') else 0,
                        hostname=self.hostname,
                        error_occurred=False
                    )
                return metrics

            # Collect connector data
            acquired_per_host = conn._acquired_per_host
            total_acquired = sum(len(connections) for connections in acquired_per_host.values())
            total_hosts = len(acquired_per_host)

            # Calculate utilization
            total_utilization_pct = (total_acquired / conn.limit * 100) if conn.limit > 0 else 0.0
            is_at_capacity = total_acquired >= conn.limit
            is_near_capacity = total_utilization_pct > 80.0

            # Determine health status
            if total_acquired == 0:
                health_status = "idle"
            elif is_at_capacity:
                health_status = "critical"
            elif is_near_capacity:
                health_status = "warning"
            else:
                health_status = "healthy"

            # Get cleanup task info
            has_cleanup_task = hasattr(conn, '_cleanup_handle') and conn._cleanup_handle is not None
            cleanup_task_active = has_cleanup_task and not conn._cleanup_handle.cancelled()

            # Collect per-host details
            host_details = []
            total_available = 0

            for key, transports in acquired_per_host.items():
                try:
                    available = conn._available_connections(key)
                    total_available += available
                    acquired_count = len(transports)

                    host_utilization_pct = 0.0
                    if conn.limit_per_host > 0:
                        host_utilization_pct = (acquired_count / conn.limit_per_host) * 100

                    host_detail = {
                        "host": key.host,
                        "port": key.port,
                        "acquired": acquired_count,
                        "available": available,
                        "total": acquired_count + available,
                        "utilization_pct": round(host_utilization_pct, 1),
                        "is_ssl": getattr(key, 'is_ssl', False),
                        "at_limit": acquired_count >= conn.limit_per_host if conn.limit_per_host > 0 else False
                    }
                    host_details.append(host_detail)

                except Exception as host_error:
                    # Log host-specific errors but continue
                    if visual_output:
                        log(f"Error processing host {key.host}:{key.port}: {host_error}")

            # Create metrics object
            if return_metrics:
                metrics = ConnectorMetrics(
                    session_name=name,
                    session_id=session_id,
                    timestamp=timestamp,
                    session_closed=session.closed,
                    connector_closed=conn._closed,
                    connector_type=type(conn).__name__,
                    total_limit=conn.limit,
                    per_host_limit=conn.limit_per_host,
                    total_acquired=total_acquired,
                    total_available=total_available,
                    active_hosts=total_hosts,
                    global_acquired_pool_size=len(conn._acquired),
                    total_utilization_pct=round(total_utilization_pct, 1),
                    is_at_capacity=is_at_capacity,
                    is_near_capacity=is_near_capacity,
                    health_status=health_status,
                    has_cleanup_task=has_cleanup_task,
                    cleanup_task_active=cleanup_task_active,
                    host_details=host_details,
                    process_id=self.process_id,
                    thread_id=threading.get_ident() if hasattr(threading, 'get_ident') else 0,
                    hostname=self.hostname,
                    error_occurred=False
                )

            # Visual output (if requested)
            if visual_output:
                if health_status in ("warning", "critical") or total_utilization_pct > 50.0:
                    self._log_visual_output(name, metrics, conn, host_details)

            # Log structured data to database
            if self.logger and return_metrics:
                if health_status in ("warning", "critical") or total_utilization_pct > 50.0:
                    self._log_structured_data(metrics, correlation_id, task_id)

        except Exception as e:
            error_occurred = True
            error_message = str(e)

            exc_type, exc_value, exc_tb = sys.exc_info()
            error_line = exc_tb.tb_lineno if exc_tb else None

            if visual_output:
                log(f"❌ Error in connector diagnostics for '{name}': {error_message}")
                if self.logger:
                    self.logger.exception(f"Connector diagnostics failed for session '{name}'")

            # Create error metrics
            if return_metrics:
                metrics = ConnectorMetrics(
                    session_name=name,
                    session_id=session_id if 'session_id' in locals() else "unknown",
                    timestamp=timestamp if 'timestamp' in locals() else datetime.now(),
                    session_closed=session.closed,
                    connector_closed=True,
                    connector_type="unknown",
                    total_limit=0,
                    per_host_limit=0,
                    total_acquired=0,
                    total_available=0,
                    active_hosts=0,
                    global_acquired_pool_size=0,
                    total_utilization_pct=0.0,
                    is_at_capacity=False,
                    is_near_capacity=False,
                    health_status="error",
                    has_cleanup_task=False,
                    cleanup_task_active=False,
                    host_details=[],
                    process_id=self.process_id,
                    thread_id=threading.get_ident() if hasattr(threading, 'get_ident') else 0,
                    hostname=self.hostname,
                    error_occurred=True,
                    error_message=error_message,
                    error_line=error_line
                )

        return metrics

    def _log_visual_output(self, name: str, metrics: ConnectorMetrics, conn, host_details: List[Dict]):
        """Generate visual console/file output"""
        log = self.logger.debug if self.logger else print

        log(f"\n{'=' * 60}")
        log(f"🔌 CONNECTION DIAGNOSTICS: {name}")
        log(f"{'=' * 60}")

        # Status section
        log(f"📊 STATUS:")
        log(f"   • Session: {'❌ CLOSED' if metrics.session_closed else '✅ OPEN'}")
        log(f"   • Connector: {'❌ CLOSED' if metrics.connector_closed else '✅ OPEN'}")
        log(f"   • Type: {metrics.connector_type}")

        # Limits section
        log(f"\n🎯 LIMITS:")
        log(f"   • Total: {metrics.total_limit}")
        log(f"   • Per-host: {metrics.per_host_limit}")

        # Statistics section
        log(f"\n📈 STATISTICS:")
        log(f"   • Acquired: {metrics.total_acquired}")
        log(f"   • Available: {metrics.total_available}")
        log(f"   • Active hosts: {metrics.active_hosts}")
        log(f"   • Utilization: {metrics.total_utilization_pct}%")

        # Health section
        status_emoji = {"healthy": "✅", "warning": "⚠️", "critical": "🔴", "idle": "ℹ️", "error": "❌"}
        log(f"\n🏥 HEALTH: {status_emoji.get(metrics.health_status, '❓')} {metrics.health_status.upper()}")

        # Host details
        if host_details:
            log(f"\n🌐 HOSTS:")
            for i, host in enumerate(host_details, 1):
                health = "🔴" if host["at_limit"] else "🟢"
                log(f"   {health} {host['host']}:{host['port']}")
                log(f"      ├─ Acquired: {host['acquired']} ({host['utilization_pct']}%)")
                log(f"      └─ Available: {host['available']}")

        log(f"{'=' * 60}")

    def _log_structured_data(self, metrics: ConnectorMetrics, correlation_id: Optional[str], task_id: Optional[str]):
        """Log structured data suitable for database storage"""
        if not self.logger:
            return

        # Create structured log entry
        extra_data = {
            "connector_metrics": asdict(metrics),
            "metric_type": "connector_diagnostics",
            "analysis_ready": True
        }

        # Performance data for separate column
        performance_data = {
            "total_utilization_pct": metrics.total_utilization_pct,
            "total_acquired": metrics.total_acquired,
            "total_available": metrics.total_available,
            "active_hosts": metrics.active_hosts,
            "is_at_capacity": metrics.is_at_capacity,
            "is_near_capacity": metrics.is_near_capacity,
            "health_status": metrics.health_status
        }

        # Log with structured data
        self.logger.info(
            f"Connector diagnostics for session '{metrics.session_name}': "
            f"{metrics.health_status} status, {metrics.total_utilization_pct}% utilization, "
            f"{metrics.total_acquired} acquired connections across {metrics.active_hosts} hosts",
            extra={
                # "correlation_id": correlation_id,
                # "task_id": task_id,
                "extra_data": extra_data,
                "performance_data": performance_data,
                "session_name": metrics.session_name,
                "session_id": metrics.session_id,
                "connector_type": metrics.connector_type,
                "health_status": metrics.health_status
            }
        )


@inject
async def fetch_with_retries(
        session: aiohttp.ClientSession,
        method: str,
        url: str,
        *,
        params: Union[Dict[str, Any], None] = None,
        json_payload: Union[Dict[str, Any], None] = None,
        retries: int = MAX_RETRIES,
        my_logger: Logger = Provide[LoggerContainer.logger],
        # global_circuit_breaker: GlobalCircuitBreaker = Provide[CircuitBreakerContainer.global_circuit_breaker],
        simplified_circuit_breaker: SimplifiedCircuitBreaker = Provide[CircuitBreakerContainer.simplified_circuit_breaker],
) -> Dict[str, Any]:
    """
    Perform an HTTP request with retry logic.

    :param session: aiohttp ClientSession.
    :param method: HTTP method (e.g., 'GET', 'POST', 'PUT').
    :param url: API endpoint URL.
    :param params: HTTP request parameters for GET.
    :param json_payload: JSON body for POST or PUT.
    :param retries: Maximum number of retries.
    :param my_logger: Logger instance.
    :param simplified_circuit_breaker: Circuit breaker instance.
    :return: Dictionary with keys:
        - 'success': bool - True if request succeeded, False otherwise
        - 'result': Any - Response data if success=True, None if success=False
        - 'exception': str - Error message if success=False, not present if success=True
    """
    asyncio.current_task().set_name(f"fetch_with_retries_{method}")
    retry_count = 0
    retry_delay = INITIAL_RETRY_DELAY

    last_exception = None
    connector_logger = ConnectorLogger(my_logger)

    while retry_count <= retries:
        # Check circuit breaker before making request
        connector_logger.log_connector_attrs(name="fetch_with_retries", session=session)
        # log_connector_attrs("fetch_with_retries", session, my_logger, show_detailed_breakdown=True)
        # Log summary (if you have multiple sessions)
        # sessions = {"main": session}
        # log_connector_summary(sessions, my_logger)

        # if not await global_circuit_breaker.can_execute():
        if not await simplified_circuit_breaker.can_execute():
            my_logger.warning("Circuit breaker is OPEN or rate limited. Waiting for recovery...")
            status = await simplified_circuit_breaker.get_status()
            backoffs = status["backoffs"]
            current_time = status["current_time"]
            # Calculate wait times for all active backoffs
            wait_times = [
                until_time - current_time
                for until_time in [
                    backoffs["rate_limit_until"],
                    backoffs["connection_pool_until"],
                    backoffs["network_until"],
                    backoffs["global_warning_until"]
                ]
                if until_time > current_time
            ]
            if wait_times:
                wait_time = max(wait_times)
                my_logger.warning(f"Circuit breaker blocked. Waiting {wait_time:.1f}s...")
                await simplified_circuit_breaker.wait_for_clearance(timeout=wait_time + 5.0)
                # await global_circuit_breaker.wait_for_recovery(timeout=300.0)


            # Check again after waiting
            # if not await global_circuit_breaker.can_execute():
            if not await simplified_circuit_breaker.can_execute():
                my_logger.warning("Circuit breaker still OPEN after waiting. Request will be deferred.")
                my_logger.debug(f"Deferred request: {url}")
                # Don't abort - return a deferral response that can be retried later
                return {"success": False, "exception": "Circuit breaker OPEN - deferred", "result": None, "deferred": True}

        # await global_circuit_breaker.enter_request()
        await simplified_circuit_breaker.enter_request()
        request_successful = False
        need_retry = False

        try:
            # async with debug_http_request(session, method, url):
            async with session.request(
                    method=method,
                    url=url,
                    params=params,
                    json=json_payload,
            ) as response:
                my_logger.debug(f"status: {response.status}, url: {response.url}")
                if response.status in (200, 201, 204):
                    # await global_circuit_breaker.record_success()
                    await simplified_circuit_breaker.record_success()
                    request_successful = True

                    # Monitor rate limit headers for proactive management
                    rate_limit_remaining = response.headers.get("X-RateLimit-Remaining")
                    rate_limit_limit = response.headers.get("X-RateLimit-Limit")
                    near_limit = response.headers.get("X-RateLimit-NearLimit") == "true"

                    if rate_limit_remaining and rate_limit_limit:
                        remaining_pct = (int(rate_limit_remaining) / int(rate_limit_limit)) * 100
                        my_logger.debug(f"Rate limit status: {rate_limit_remaining}/{rate_limit_limit} ({remaining_pct:.1f}% remaining)")

                    # Success handling
                    if response.status == 204:
                        my_logger.info("Request successful, but no content to return.")
                        return {
                            "success": True, "result": None,
                            "metadata": {
                                "status": response.status, "url": response.url,
                                "headers": dict(response.headers),
                                "params": params, "json_payload": json_payload
                            }
                        }
                    else:
                        if near_limit:
                            # Coordinate global rate limit warning across all threads
                            warning_duration = max(2.0, 2 ** retry_count)  # At least 2 seconds
                            my_logger.warning(f"Warning: Less than 20% of the rate limit budget remains.")
                            my_logger.warning(f"Activating global rate limit warning for {warning_duration} seconds.")
                            # await global_circuit_breaker.record_rate_limit_warning(warning_duration)
                            await simplified_circuit_breaker.record_rate_limit_warning(warning_duration)
                            # Wait for the global warning to clear
                            # await global_circuit_breaker.wait_for_recovery()
                            await simplified_circuit_breaker.wait_for_clearance(timeout=warning_duration)

                        return {
                            "success": True, "result": await response.json(),
                            "metadata": {
                                "status": response.status, "url": response.url,
                                "headers": dict(response.headers),
                                "params": params, "json_payload": json_payload
                            }
                        }

                elif response.status == 429 or "Retry-After" in response.headers:
                    # Rate limit - use enhanced error handling
                    retry_delay = await calculate_retry_delay(response, retry_delay)
                    rate_limit_tracker.record_rate_limit(url, retry_delay)

                    # Create rate limit exception for proper classification
                    rate_limit_error = Exception(f"Rate limit (429) - Retry-After: {retry_delay}ms")
                    # await global_circuit_breaker.record_error(rate_limit_error, retry_delay)
                    await simplified_circuit_breaker.record_error(rate_limit_error, retry_delay)
                    need_retry = True

                    my_logger.warning(
                        f"Rate limited. Waiting for coordinated recovery. "
                        f"Retry delay: {retry_delay / 1000:.2f}s, "
                        f"Consecutive rate limits: {rate_limit_tracker.consecutive_rate_limits}"
                    )

                    # Wait for rate limit specific recovery
                    # await global_circuit_breaker.wait_for_recovery(error_type=ErrorType.RATE_LIMIT)
                    await simplified_circuit_breaker.wait_for_clearance(timeout=retry_delay / 1000)

                elif response.status == 503:
                    # Service unavailable - service error
                    retry_delay = await calculate_retry_delay(response, retry_delay)
                    service_error = Exception(f"Service unavailable (503)")
                    # await global_circuit_breaker.record_error(service_error)
                    await simplified_circuit_breaker.record_error(service_error)
                    need_retry = True
                    my_logger.warning(f"Service unavailable (503). Will retry after backoff.")

                else:
                    # Other HTTP errors - classify and handle appropriately
                    http_error = Exception(f"HTTP {response.status}")
                    # await global_circuit_breaker.record_error(http_error)
                    await simplified_circuit_breaker.record_error(http_error)
                    my_logger.error(f"Request failed with status {response.status}. url = {response.url}")
                    return {
                        "success": False, "exception": f"HTTP {response.status}",
                        "metadata": {
                            "status": response.status, "url": response.url,
                            "headers": dict(response.headers),
                            "params": params, "json_payload": json_payload
                        }
                    }
                # response.raise_for_status()

        except aiohttp.ClientResponseError as e:
            my_logger.info(f"HTTP error {e.status}: {e.message}")
            last_exception = f"HTTP error {e.status}: {e.message}"

            # Use enhanced error handling with proper classification
            # await global_circuit_breaker.record_error(e)
            await simplified_circuit_breaker.record_error(e)
            need_retry = True  # Allow retry for retriable HTTP errors

            # Wait for appropriate recovery based on error type
            # error_type = global_circuit_breaker.classify_error(e)
            error_type = simplified_circuit_breaker.classify_error(e)
            if error_type == ErrorType.RATE_LIMIT:
                # await global_circuit_breaker.wait_for_recovery(error_type=ErrorType.RATE_LIMIT)
                await simplified_circuit_breaker.wait_for_clearance()

        except aiohttp.client_exceptions.ConnectionTimeoutError as e:
            my_logger.warning(f"Connection timeout: {e}. Will retry with network error handling.")
            last_exception = f"Connection timeout: {str(e)}"

            # Network error - enhanced handling with increased retries
            # await global_circuit_breaker.record_error(e)
            await simplified_circuit_breaker.record_error(e)
            need_retry = True

            # Wait for network recovery
            # await global_circuit_breaker.wait_for_recovery(error_type=ErrorType.NETWORK)
            await simplified_circuit_breaker.wait_for_clearance()

        except aiohttp.client_exceptions.ClientConnectionResetError as e:
            my_logger.warning(f"Connection reset: {e}. Will retry with network error handling.")
            last_exception = f"Connection reset: {str(e)}"

            # Network error - enhanced handling
            # await global_circuit_breaker.record_error(e)
            await simplified_circuit_breaker.record_error(e)
            need_retry = True

            # Wait for network recovery
            # await global_circuit_breaker.wait_for_recovery(error_type=ErrorType.NETWORK)
            # await simplified_circuit_breaker.wait_for_clearance()

        except aiohttp.ClientSSLError as e:
            my_logger.warning(f"SSL error: {e}. Will retry with appropriate handling.")
            last_exception = str(e)

            # Use enhanced error classification
            # await global_circuit_breaker.record_error(e)
            await simplified_circuit_breaker.record_error(e)
            need_retry = True

            # Wait for appropriate recovery
            # error_type = global_circuit_breaker.classify_error(e)
            error_type = simplified_circuit_breaker.classify_error(e)
            # await global_circuit_breaker.wait_for_recovery(error_type=error_type)
            await simplified_circuit_breaker.wait_for_clearance()

        except aiohttp.ClientConnectorError as e:
            my_logger.warning(f"Connection pool/connector error: {e}. Will retry with backoff.")
            last_exception = f"Connector error: {str(e)}"

            # Connection pool error - don't abort processes
            # await global_circuit_breaker.record_error(e)
            await simplified_circuit_breaker.record_error(e)
            need_retry = True

            # Wait for connection pool recovery
            # await global_circuit_breaker.wait_for_recovery(error_type=ErrorType.CONNECTION_POOL)
            await simplified_circuit_breaker.wait_for_clearance()

        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            my_logger.warning(f"Client/timeout error: {e}. Will retry with appropriate handling.")
            last_exception = str(e)

            # Use enhanced error classification
            # await global_circuit_breaker.record_error(e)
            await simplified_circuit_breaker.record_error(e)
            need_retry = True

            # Wait for appropriate recovery
            # error_type = global_circuit_breaker.classify_error(e)
            error_type = simplified_circuit_breaker.classify_error(e)
            # await global_circuit_breaker.wait_for_recovery(error_type=error_type)
            await simplified_circuit_breaker.wait_for_clearance()


        except Exception as e:
            my_logger.error(f"Unexpected error: {e}")
            # For unexpected errors, don't retry and return failure immediately
            return {
                "success": False, "exception": f"Unexpected error: {str(e)}",
                "metadata": {
                    "status": None, "url": url,
                    "headers": None,
                    "params": params, "json_payload": json_payload
                }
            }
        finally:
            # await global_circuit_breaker.exit_request()
            await simplified_circuit_breaker.exit_request()

        # If request was successful, return immediately (no retry needed)
        if request_successful:
            break

        # Handle retry logic with enhanced error-specific retry counts
        if need_retry:
            # Get circuit status to determine effective retry limit
            # circuit_status = await global_circuit_breaker.get_circuit_status()
            circuit_status = await simplified_circuit_breaker.get_status()
            effective_retries = retries

            # For network errors, use doubled retry count
            if last_exception and any(keyword in last_exception.lower() for keyword in [
                "connection timeout", "connection reset", "network", "dns", "ssl"
            ]):
                effective_retries = retries * circuit_status.get("network_retry_multiplier", 2)
                my_logger.debug(f"Network error detected. Using enhanced retry count: {effective_retries}")

            if retry_count < effective_retries:
                retry_count += 1

                # Only add jitter if circuit breaker isn't coordinating delays
                # and we're not dealing with rate limits (which have their own coordination)
                # if (global_circuit_breaker.state == CircuitState.CLOSED and
                #         not (retry_count == 1)):  # Skip jitter on first retry to be more responsive
                #
                #     jitter = random.uniform(0.8, 1.2)  # JITTER_MULTIPLIER_RANGE
                #     jitter_delay = round(retry_delay * jitter / 1000, 2)
                #     my_logger.debug(f"Adding jitter of {jitter:.2f}. Sleeping for {jitter_delay} seconds.")
                #     await asyncio.sleep(jitter_delay)
            else:
                # Exhausted retries
                my_logger.warning(f"Exceeded maximum retries ({effective_retries}) for this error type.")
                break
        else:
            # No retry needed
            break

        # Handle retry delay for non-coordinated retries
        if retry_count > effective_retries:
            my_logger.error(f"Exceeded maximum retries ({effective_retries}).")
            break

        # Add minimal jitter for non-coordinated retries
        # if global_circuit_breaker.state == CircuitState.CLOSED:
        #     jitter = random.uniform(*JITTER_MULTIPLIER_RANGE)
        #     minimal_delay = (retry_delay + jitter) / 1000
        #     if minimal_delay > 0.1:  # Only sleep if delay is meaningful
        #         await asyncio.sleep(minimal_delay)

    # If we reach here, all retries have been exhausted
    # Return failure response with the last exception encountered
    if last_exception:
        my_logger.error(f"All retries exhausted. Last error: {last_exception}")

        # Classify the error to determine if shutdown is needed
        if last_exception:
            try:
                # Create exception object for classification
                if "rate limit" in last_exception.lower() or "429" in last_exception:
                    error_for_classification = Exception(f"Rate limit: {last_exception}")
                elif "connection" in last_exception.lower():
                    error_for_classification = Exception(f"Connection error: {last_exception}")
                else:
                    error_for_classification = Exception(last_exception)

                # error_type = global_circuit_breaker.classify_error(error_for_classification)
                error_type = simplified_circuit_breaker.classify_error(error_for_classification)

                # Only trigger shutdown for unrecoverable errors or excessive service failures
                # if error_type in [ErrorType.UNRECOVERABLE] or (
                #     error_type == ErrorType.SERVICE and
                #     # global_circuit_breaker.state == CircuitState.OPEN
                #     simplified_circuit_breaker.get_status() == CircuitState.OPEN
                # ):
                #     await global_circuit_breaker.trigger_graceful_shutdown(
                #         reason=f"Unrecoverable error after all retries: {last_exception}"
                #     )
                # else:
                #     my_logger.warning(f"Recoverable error after retries - will be deferred: {last_exception}")

            except Exception as e:
                my_logger.error(f"Error during error classification: {e}")

        return {
            "success": False, "exception": last_exception, "deferred": True,
            "metadata": {
                "status": None, "url": url,
                "headers": None,
                "params": params, "json_payload": json_payload
            }
        }
    else:
        my_logger.error("All retries exhausted with no specific error recorded.")
        # This is unusual and might indicate a logic error
        # await global_circuit_breaker.trigger_graceful_shutdown(
        #     reason="All retries exhausted in fetch_with_retries with no specific error recorded."
        # )

        return {
            "success": False, "exception": "Maximum retries exceeded",
            "metadata": {
                "status": None, "url": url,
                "headers": None,
                "params": params, "json_payload": json_payload
            }
        }


async def fetch_with_retries_post(
        session: aiohttp.ClientSession,
        url: str,
        json_payload: Dict[str, Any]
) -> Dict[str, Any]:
    """
    POST wrapper for fetch_with_retries.

    :return: Dictionary with keys 'success', 'result', and 'exception' (if failed)
    """
    asyncio.current_task().set_name("fetch_with_retries_post")
    return await fetch_with_retries(
        session=session,
        method="POST",
        url=url,
        json_payload=json_payload,
    )


async def fetch_with_retries_get(
        session: aiohttp.ClientSession,
        url: str,
        params: Union[Dict[str, Any], None] = None,
) -> Dict[str, Any]:
    """
    GET wrapper for fetch_with_retries.

    :return: Dictionary with keys 'success', 'result', and 'exception' (if failed)
    """
    asyncio.current_task().set_name("fetch_with_retries_get")
    return await fetch_with_retries(
        session=session,
        method="GET",
        url=url,
        params=params
    )


async def calculate_retry_delay(
        response: aiohttp.ClientResponse, last_retry_delay: float,
        my_logger: Logger = Provide[LoggerContainer.logger]
) -> float:
    """
    Calculate the delay for the next retry attempt.

    :param response: The HTTP response object.
    :param last_retry_delay: The last retry delay in milliseconds.
    :param my_logger: logger instance Injected from container
    :return: The new retry delay in milliseconds.
    """
    retry_delay = -1

    # Check for the Retry-After header
    if "Retry-After" in response.headers:
        retry_delay = int(response.headers["Retry-After"]) * 1000  # Convert to milliseconds

    # Check for the X-RateLimit-Reset header
    elif "X-RateLimit-Reset" in response.headers:
        reset_time = parser.parse(response.headers["X-RateLimit-Reset"])
        current_time = datetime.now(timezone.utc)
        wait_time = (reset_time - current_time).total_seconds() * 1000  # Convert to milliseconds
        retry_delay = max(wait_time, last_retry_delay)

    # If no headers are present but 429 status is received, double the last delay
    elif response.status == 429:
        retry_delay = min(2 * last_retry_delay, MAX_RETRY_DELAY)

    elif response.status in (503, 500) and "Retry-After" in response.headers:
        # Handle transient 5XX errors with Retry-After header
        retry_delay = int(response.headers["Retry-After"]) * 1000  # Convert to milliseconds

    # Apply jitter
    if retry_delay > 0:
        jitter = random.uniform(*JITTER_MULTIPLIER_RANGE)
        retry_delay += int(retry_delay * jitter)

    # Log and handle X-RateLimit-NearLimit
    if response.headers.get("X-RateLimit-NearLimit") == "true":
        retry_delay = max(retry_delay, 5000)  # Example delay of 5 seconds
        my_logger.warning(f"Less than 20% of the budget remains. sleeping for {retry_delay / 1000} seconds.")
        # Introduce a delay when near limit is reached
        await asyncio.sleep(retry_delay / 1000)

    return retry_delay


logger_container = LoggerContainer()
logger_container.wire(modules=["dags.data_pipeline.jira.api_client"])

application_container = ApplicationContainer()
application_container.wire(modules=["dags.data_pipeline.jira.api_client"])
circuit_breaker_container = CircuitBreakerContainer()
circuit_breaker_container.wire(modules=["dags.data_pipeline.jira.api_client"])
