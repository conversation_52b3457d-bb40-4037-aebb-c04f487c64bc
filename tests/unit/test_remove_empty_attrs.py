import pytest
import json

from dags.data_pipeline.utility_code import remove_empty_attrs


@pytest.fixture
def sample_comment_json():
    return {
        "type": "doc",
        "version": 1,
        "content": [
            {
                "type": "table",
                "attrs": {"layout": "default"},
                "content": [
                    {
                        "type": "tableRow",
                        "content": [
                            {
                                "type": "tableCell",
                                "attrs": {},
                                "content": [
                                    {
                                        "type": "paragraph",
                                        "content": [
                                            {"type": "text", "text": "Sample text"}
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }

def test_remove_empty_attrs_removes_tableCell_attrs(sample_comment_json):
    cleaned = remove_empty_attrs(sample_comment_json)

    # Traverse to the tableCell node
    table_cell = (
        cleaned["content"][0]["content"][0]["content"][0]
    )  # First tableCell
    print(json.dumps(cleaned, indent=2))

    assert "attrs" not in table_cell, "'attrs' should be removed from tableCell"

def test_remove_non_empty_attrs_remain():
    input_obj = {
        "type": "tableCell",
        "attrs": {"colspan": 2},
        "content": []
    }

    result = remove_empty_attrs(input_obj)
    assert "attrs" in result, "Non-empty 'attrs' should not be removed"
    assert result["attrs"] == {"colspan": 2}
