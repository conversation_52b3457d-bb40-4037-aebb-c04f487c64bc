# coding=utf-8
"""
Comprehensive test suite for enhanced dependency injection containers.

This test suite covers:
- CoreSessionManagerContainer functionality
- TaskCoordinationContainer components
- EnhancedApplicationContainer integration
- Enhanced shutdown handler coordination
- Container composition and dependency injection
"""

import pytest
import allure
import asyncio
from unittest.mock import MagicMock, patch

from dags.data_pipeline.containers import (
    CoreSessionManagerContainer,
    TaskCoordinationContainer,
    EnhancedApplicationContainer,
    EntryDetails
)
from dags.data_pipeline.utils.shutdown_handler import ApplicationShutdownHandler
from dags.data_pipeline.utility_code import TaskLifecycleCoordinator


@pytest.fixture
@allure.title("Mock Entry Details")
def mock_entry_details():
    """Fixture providing mock database entry details."""
    return EntryDetails(
        username="test_user",
        password="test_password",
        url="postgresql://test_user:test_password@localhost:5432/testdb",
        custom_properties={
            "DB_SERVER_NAME": "localhost",
            "DB_SERVER_RW_PORT": 5432,
            "DB_SERVER_RO_PORT": 5433,
            "DB_NAME": "testdb"
        }
    )


@pytest.fixture
@allure.title("Mock Logger")
def mock_logger():
    """Fixture providing a mock logger."""
    return MagicMock()


@allure.epic("Enhanced Dependency Injection")
@allure.feature("Core Session Manager Container")
class TestCoreSessionManagerContainer:
    """Test suite for CoreSessionManagerContainer."""
    
    @allure.story("Container Configuration")
    @allure.title("Should configure session managers correctly")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    def test_session_manager_configuration(self, mock_create_sync_engine, mock_create_async_engine,
                                           mock_entry_details, mock_logger):
        """Test session manager configuration in container."""
        from dependency_injector import containers, providers
        # Setup mocks
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = MagicMock()
        mock_async_engine_with_options = MagicMock()
        
        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine

        # Create container
        container = CoreSessionManagerContainer()
        container.schema.override("test_schema")

        mock_logger_container = containers.DynamicContainer()
        mock_logger_container.logger = providers.Object(mock_logger)
        container.logger_container.override(mock_logger_container)
        # container(logger_container={'logger': mock_logger})
        # container.logger_container.override({'logger': mock_logger})
        container.pg_rw_entry.override(mock_entry_details)
        container.pg_ro_entry.override(mock_entry_details)
        
        # Test base session manager creation
        base_rw = container.base_session_manager_rw()
        assert base_rw.schema == "test_schema"
        assert base_rw.rw is True
        assert base_rw.logger == mock_logger
        
        base_ro = container.base_session_manager_ro()
        assert base_ro.schema == "test_schema"
        assert base_ro.rw is False
        assert base_ro.logger == mock_logger
        
    @allure.story("Factory Pattern")
    @allure.title("Should support factory pattern for testing")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    def test_factory_pattern_support(self, mock_create_sync_engine, mock_create_async_engine, 
                                   mock_entry_details, mock_logger):
        """Test that container supports factory pattern for easy testing."""
        # Setup mocks
        from dependency_injector import containers, providers
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = MagicMock()
        mock_async_engine_with_options = MagicMock()
        
        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine
        
        # Create container
        container = CoreSessionManagerContainer()
        container.schema.override("test_schema")

        mock_logger_container = containers.DynamicContainer()
        mock_logger_container.logger = providers.Object(mock_logger)
        container.logger_container.override(mock_logger_container)

        # container.logger_container.override({'logger': mock_logger})
        container.pg_rw_entry.override(mock_entry_details)
        container.pg_ro_entry.override(mock_entry_details)
        
        # Test that factory creates new instances each time
        manager1 = container.base_session_manager_rw()
        manager2 = container.base_session_manager_rw()
        
        assert manager1 is not manager2  # Factory should create new instances
        
        # Test that singleton creates same instance
        managed1 = container.managed_session_manager_rw()
        managed2 = container.managed_session_manager_rw()
        
        assert managed1 is managed2  # Singleton should return same instance


@allure.epic("Enhanced Dependency Injection")
@allure.feature("Task Coordination Container")
class TestTaskCoordinationContainer:
    """Test suite for TaskCoordinationContainer."""
    
    @allure.story("Task Lifecycle Coordinator")
    @allure.title("Should provide task lifecycle coordinator")
    def test_task_lifecycle_coordinator_provision(self, mock_logger):
        """Test task lifecycle coordinator provision."""
        from dependency_injector import containers, providers

        # Create a mock logger container
        mock_logger_container = containers.DynamicContainer()
        mock_logger_container.logger = providers.Object(mock_logger)

        container = TaskCoordinationContainer()
        container.logger_container.override(mock_logger_container)

        coordinator = container.task_lifecycle_coordinator()

        assert isinstance(coordinator, TaskLifecycleCoordinator)
        assert coordinator.logger == mock_logger
        
    @allure.story("Shutdown Handler")
    @allure.title("Should provide enhanced shutdown handler")
    def test_enhanced_shutdown_handler_provision(self, mock_logger):
        """Test enhanced shutdown handler provision."""
        from dependency_injector import containers, providers

        # Create a mock logger container
        mock_logger_container = containers.DynamicContainer()
        mock_logger_container.logger = providers.Object(mock_logger)

        container = TaskCoordinationContainer()
        container.logger_container.override(mock_logger_container)

        shutdown_handler = container.enhanced_shutdown_handler()

        assert isinstance(shutdown_handler, ApplicationShutdownHandler)

        # Test singleton behavior
        shutdown_handler2 = container.enhanced_shutdown_handler()
        assert shutdown_handler is shutdown_handler2


@allure.epic("Enhanced Dependency Injection")
@allure.feature("Enhanced Application Container")
class TestEnhancedApplicationContainer:
    """Test suite for EnhancedApplicationContainer."""
    
    @allure.story("Container Composition")
    @allure.title("Should compose containers correctly")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    def test_container_composition(self, mock_create_sync_engine, mock_create_async_engine, 
                                 mock_entry_details, mock_logger):
        """Test container composition and dependency injection."""
        from dependency_injector import containers, providers
        # Setup mocks
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = MagicMock()
        mock_async_engine_with_options = MagicMock()
        
        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine
        
        # Create mock containers
        # mock_logger_container = {'logger': mock_logger}
        # mock_keepass_container = {
        #     'pg_rw': mock_entry_details,
        #     'pg_ro': mock_entry_details
        # }

        mock_logger_container = containers.DynamicContainer()
        mock_logger_container.logger = providers.Object(mock_logger)

        mock_keepass_container = containers.DynamicContainer()
        mock_keepass_container.pg_rw = providers.Object(mock_entry_details)
        mock_keepass_container.pg_ro = providers.Object(mock_entry_details)
        
        # Create container
        container = EnhancedApplicationContainer()
        container.schema.override("test_schema")
        container.logger_container.override(mock_logger_container)
        container.keepass_container.override(mock_keepass_container)
        
        # Test database access
        db_rw = container.database_rw()
        assert db_rw.schema == "test_schema"
        assert db_rw.rw is True
        
        db_ro = container.database_ro()
        assert db_ro.schema == "test_schema"
        assert db_ro.rw is False
        
    @allure.story("Easy Access Providers")
    @allure.title("Should provide easy access to components")
    def test_easy_access_providers(self, mock_logger):
        """Test easy access providers for commonly used components."""
        from dependency_injector import containers, providers
        # Create mock containers
        mock_logger_container = containers.DynamicContainer()
        mock_logger_container.logger = providers.Object(mock_logger)
        
        # Create container
        container = EnhancedApplicationContainer()
        container.schema.override("test_schema")
        container.logger_container.override(mock_logger_container)
        
        # Test task coordinator access
        coordinator = container.task_coordinator()
        assert isinstance(coordinator, TaskLifecycleCoordinator)
        
        # Test shutdown handler access
        shutdown_handler = container.shutdown_handler()
        assert isinstance(shutdown_handler, ApplicationShutdownHandler)


@allure.epic("Enhanced Dependency Injection")
@allure.feature("Enhanced Shutdown Handler")
class TestEnhancedApplicationShutdownHandler:
    """Test suite for EnhancedApplicationShutdownHandler."""
    
    @allure.story("Component Registration")
    @allure.title("Should register and unregister components")
    @pytest.mark.asyncio
    async def test_component_registration(self):
        """Test component registration and unregistration."""
        handler = ApplicationShutdownHandler()
        
        # Register components
        await handler.register_component("component_1")
        await handler.register_component("component_2")
        
        assert "component_1" in handler._active_components
        assert "component_2" in handler._active_components
        assert len(handler._active_components) == 2
        
        # Unregister component
        await handler.unregister_component("component_1")
        
        assert "component_1" not in handler._active_components
        assert "component_2" in handler._active_components
        assert len(handler._active_components) == 1
        
    @allure.story("Coordinated Shutdown")
    @allure.title("Should coordinate shutdown with components")
    @pytest.mark.asyncio
    async def test_coordinated_shutdown(self):
        """Test coordinated shutdown with component unregistration."""
        handler = ApplicationShutdownHandler()
        
        # Register components
        await handler.register_component("component_1")
        await handler.register_component("component_2")
        
        # Simulate component shutdown
        async def simulate_component_shutdown():
            await asyncio.sleep(0.1)  # Simulate some work
            await handler.unregister_component("component_1")
            await asyncio.sleep(0.1)  # Simulate some work
            await handler.unregister_component("component_2")
            
        # Start component shutdown simulation
        shutdown_task = asyncio.create_task(simulate_component_shutdown())
        
        # Start coordinated shutdown
        with patch.object(handler, '_cleanup_session_managers') as mock_cleanup:
            await handler.shutdown(timeout=5.0)
            
        # Wait for simulation to complete
        await shutdown_task
        
        assert handler.is_shutting_down()
        assert len(handler._active_components) == 0
        
    @allure.story("Timeout Handling")
    @allure.title("Should handle shutdown timeouts gracefully")
    @pytest.mark.asyncio
    async def test_shutdown_timeout(self):
        """Test shutdown timeout handling."""
        handler = ApplicationShutdownHandler()
        
        # Register a component that won't unregister itself
        await handler.register_component("stuck_component")
        
        # Test shutdown with short timeout
        start_time = asyncio.get_event_loop().time()
        with patch.object(handler, '_cleanup_session_managers'):
            await handler.shutdown(timeout=0.1)
        end_time = asyncio.get_event_loop().time()
        
        # Should have timed out quickly
        assert end_time - start_time < 0.5
        assert handler.is_shutting_down()
        assert "stuck_component" in handler._active_components  # Component still active


@allure.epic("Enhanced Dependency Injection")
@allure.feature("Integration Tests")
class TestContainerIntegration:
    """Integration tests for container interactions."""
    
    @allure.story("Full Integration")
    @allure.title("Should integrate all containers seamlessly")
    @patch('dags.data_pipeline.containers.create_async_engine')
    @patch('dags.data_pipeline.containers.create_engine')
    @pytest.mark.asyncio
    async def test_full_container_integration(self, mock_create_sync_engine, mock_create_async_engine, 
                                            mock_entry_details, mock_logger):
        """Test full integration of all enhanced containers."""
        from dependency_injector import containers, providers

        # Setup mocks
        mock_sync_engine = MagicMock()
        mock_sync_engine_with_options = MagicMock()
        mock_async_engine = MagicMock()
        mock_async_engine_with_options = MagicMock()
        
        mock_sync_engine.execution_options.return_value = mock_sync_engine_with_options
        mock_async_engine.execution_options.return_value = mock_async_engine_with_options
        mock_create_sync_engine.return_value = mock_sync_engine
        mock_create_async_engine.return_value = mock_async_engine
        
        # Create mock containers

        mock_keepass_container = containers.DynamicContainer()
        mock_keepass_container.pg_rw = providers.Object(mock_entry_details)
        mock_keepass_container.pg_ro = providers.Object(mock_entry_details)
        
        # Create and configure container
        container = EnhancedApplicationContainer()
        container.schema.override("integration_test")

        mock_logger_container = containers.DynamicContainer()
        mock_logger_container.logger = providers.Object(mock_logger)

        container.logger_container.override(mock_logger_container)
        container.keepass_container.override(mock_keepass_container)
        
        # Test database session creation
        db_rw = container.database_rw()
        assert db_rw.schema == "integration_test"
        
        # Test task coordination
        coordinator = container.task_coordinator()
        task_id = await coordinator.register_task("integration_test_task")
        assert task_id in coordinator._active_tasks
        
        # Test shutdown coordination
        shutdown_handler = container.shutdown_handler()
        await shutdown_handler.register_component("test_component")
        assert "test_component" in shutdown_handler._active_components
        
        # Cleanup
        await coordinator.mark_task_completed(task_id, "success")
        await shutdown_handler.unregister_component("test_component")
        
        assert len(coordinator._active_tasks) == 0
        assert len(shutdown_handler._active_components) == 0
